upstream backend {
    server backtest_backend:8000;
}

server {
    listen 8080;
    server_name backend.backtest.localhost;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Proxy settings
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Increase proxy timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Main location block for Django backend
    location / {
        proxy_pass http://backend;
        proxy_redirect off;
    }

    # Static files (if served by Django in development)
    location /static/ {
        proxy_pass http://backend;
    }

    # Media files (if served by Django in development)
    location /media/ {
        proxy_pass http://backend;
    }

    # Health check endpoint
    location /health/ {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
